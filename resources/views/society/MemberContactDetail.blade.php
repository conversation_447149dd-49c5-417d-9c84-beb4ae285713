<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Member Contact Detail' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #000; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
        .name-col { width: 25%; text-align: left; }
        .unit-col { width: 25%; text-align: left; }
        .email-col { width: 30%; text-align: left; }
        .mobile-col { width: 20%; text-align: right; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Member Contact Detail' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Member Contact Detail</div>
    <div class="filter-info">Filter: Email - All, Mobile - All</div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data)) {
                $reportData = $data;
            }
        @endphp

        @if(!empty($reportData))
            
            <table>
                <thead>
                    <tr>
                        <th class="small-font name-col">Member Name</th>
                        <th class="small-font unit-col">Unit Name</th>
                        <th class="small-font email-col">Email Address</th>
                        <th class="small-font mobile-col">Mobile Number</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            <td class="small-font name-col">{{ $row['member_name'] ?? 'N/A' }}</td>
                            <td class="small-font unit-col">{{ $row['unit_name'] ?? 'N/A' }}</td>
                            <td class="small-font email-col">{{ $row['member_email_id'] ?? 'N/A' }}</td>
                            <td class="small-font mobile-col">{{ $row['member_mobile_number'] ?? 'N/A' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

        @else
            <div class="text-center" style="padding: 20px;">
                <p>No member contact data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
