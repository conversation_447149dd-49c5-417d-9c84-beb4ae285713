<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Member Advances Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #000; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .building-unit-col { width: 60%; text-align: left; }
        .balance-col { width: 20%; text-align: right; }

    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Member Advances Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Member Advance</div>



    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];
            $summaryData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data)) {
                if (isset($data[0]) && is_array($data[0])) {
                    $reportData = $data[0];
                }
                if (isset($data[1]) && is_array($data[1]) && !empty($data[1])) {
                    $summaryData = is_array($data[1][0]) ? $data[1][0] : [];
                }
            }
        @endphp

        @if(!empty($reportData))

            <table>
                <thead>
                    <tr>
                        <th class="small-font building-unit-col">Building/Unit</th>
                        <th class="small-font balance-col">Refundable Balance</th>
                        <th class="small-font balance-col">Adjustable Balance</th>
                    </tr>
                </thead>
                <tbody>
                    @if(!empty($reportData))
                        @foreach($reportData as $row)
                            <tr>
                                <td class="small-font building-unit-col">{{ $row['building_unit_name'] ?? 'N/A' }}</td>
                                <td class="small-font balance-col">
                                    @if(isset($row['total_refundable']))
                                        @if($row['total_refundable'] == '0.00' || $row['total_refundable'] == 0)
                                            0.00
                                        @elseif(strpos($row['total_refundable'], '-') === 0)
                                            {{ $row['total_refundable'] }}
                                        @else
                                            {{ $row['total_refundable'] }}
                                        @endif
                                    @else
                                        0.00
                                    @endif
                                </td>
                                <td class="small-font balance-col">
                                    @if(isset($row['total_adjustable']))
                                        @if($row['total_adjustable'] == '0.00' || $row['total_adjustable'] == 0)
                                            0.00
                                        @else
                                            {{ $row['total_adjustable'] }}
                                        @endif
                                    @else
                                        0.00
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    @endif

                    {{-- Summary Row --}}
                    @if(!empty($summaryData))
                        <tr class="total-row">
                            <td class="small-font building-unit-col"><strong>SUM</strong></td>
                            <td class="small-font balance-col"><strong>{{ number_format($summaryData['total_summary_refundable'] ?? 0, 2) }}</strong></td>
                            <td class="small-font balance-col"><strong>{{ number_format($summaryData['total_summary_adjustable'] ?? 0, 2) }}</strong></td>
                        </tr>
                    @endif
                </tbody>
            </table>



        @else
            <div class="text-center" style="padding: 20px;">
                <p>No data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
