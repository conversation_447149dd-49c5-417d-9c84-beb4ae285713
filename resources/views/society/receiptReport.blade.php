<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Receipt Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #000; text-align: left; font-size: 10px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 9px; }
        .date-col { width: 10%; text-align: center; }
        .receipt-col { width: 10%; text-align: center; }
        .from-col { width: 12%; text-align: left; }
        .mode-col { width: 8%; text-align: center; }
        .paid-by-col { width: 15%; text-align: left; }
        .payment-of-col { width: 15%; text-align: left;text
        .reference-col { width: 15%; text-align: left; }
        .unit-col { width: 12%; text-align: left; }
        .amount-col { width: 10%; text-align: right; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Receipt Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Receipt Report</div>
    <div class="filter-info">Filter: Unit - All, From date - {{ $date_from ?? '01/07/2025' }}, To date - {{ $date_to ?? '31/07/2025' }}, Status - All, Type - All, Mode - All</div>
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];
            $summaryData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data) && count($data) >= 2) {
                $reportData = $data[0] ?? [];
                $summaryData = $data[1][0] ?? [];
            }
        @endphp

        @if(!empty($reportData))

            <table>
                <thead>
                    <tr>
                        <th class="small-font date-col">Receipt Date</th>
                        <th class="small-font receipt-col">Receipt No</th>
                        <th class="small-font from-col">Receipt From</th>
                        <th class="small-font mode-col">Mode</th>
                        <th class="small-font paid-by-col">Paid By</th>
                        <th class="small-font payment-of-col">Payment Of</th>
                        <th class="small-font reference-col">Payment Reference</th>
                        <th class="small-font unit-col">Building / Unit</th>
                        <th class="small-font amount-col">Paid Amount(₹)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            <td class="small-font date-col">{{ date('d/m/Y', strtotime($row['payment_date'] ?? '')) }}</td>
                            <td class="small-font receipt-col">{{ $row['receipt_number'] ?? 'N/A' }}</td>
                            <td class="small-font from-col">
                                @if($row['bill_type'] == 'member')
                                    Non Member
                                @else
                                    Incidental
                                @endif
                            </td>
                            <td class="small-font mode-col">{{ ucfirst($row['payment_mode'] ?? 'N/A') }}</td>
                            <td class="small-font paid-by-col">{{ $row['received_from'] ?? 'N/A' }}</td>
                            <td class="small-font payment-of-col">{{ $row['invoice_number'] ?? 'N/A' }}</td>
                            <td class="small-font reference-col">
                                @if($row['payment_mode'] == 'cheque' && !empty($row['transaction_reference']))
                                    CHQ:{{ $row['transaction_reference'] }} / {{ $row['payment_instrument'] ?? '' }}
                                @else
                                    {{ $row['transaction_reference'] ?? 'N/A' }}
                                @endif
                            </td>
                            <td class="small-font unit-col">{{ $row['society_unit_name'] ?? 'N/A' }}</td>
                            <td class="small-font amount-col">{{ number_format($row['payment_amount'] ?? 0, 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            {{-- Summary Table --}}
            @if(!empty($summaryData))
                <div style="margin-top: 30px;">
                    <h3 style="margin-bottom: 10px;">Summary</h3>
                    <table style="width: 60%; margin: 0;">
                        <thead>
                            <tr>
                                <th class="small-font text-center" style="width: 33.33%;">Paid Amount</th>
                                <th class="small-font text-center" style="width: 33.33%;">TDS Deducted</th>
                                <th class="small-font text-center" style="width: 33.33%;">Write Off Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="small-font text-center">{{ number_format($summaryData['paid_amount_total'] ?? 0, 2) }}</td>
                                <td class="small-font text-center">{{ number_format($summaryData['tds_deducted_total'] ?? 0, 2) }}</td>
                                <td class="small-font text-center">{{ number_format($summaryData['write_off_total'] ?? 0, 2) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            @endif

        @else
            <div class="text-center" style="padding: 20px;">
                <p>No receipt data available for the selected criteria.</p>
            </div>
        @endif
    </div>
    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
